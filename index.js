import Logger from './logger.js'

// Create a basic logger
const logger = new Logger()

// Example usage
logger.header('Logger Demo')

logger.info('This is an info message')
logger.success('Operation completed successfully!')
logger.warn('This is a warning message')
logger.error('This is an error message')
logger.debug('This is a debug message')

logger.separator()

// Create logger with custom options
const customLogger = new Logger({
	// showTimestamp: true,
	showLevel: false
	// prefix: 'MyApp'
})

customLogger.info('Logger with custom prefix')
customLogger.success('Data saved to database')

logger.separator()

// Create logger with prefix using withPrefix method
const dbLogger = logger.withPrefix('Database')
const apiLogger = logger.withPrefix('API')

dbLogger.info('Connected to database')
apiLogger.info('Server started on port 3000')

logger.separator()

// Custom colored messages
logger.custom('cyan', 'CUSTOM', 'This is a custom cyan message')
logger.custom('magenta', 'SPECIAL', 'This is a special magenta message')

// Logger without timestamp and level
const simpleLogger = new Logger({
	showTimestamp: false,
	showLevel: false
})

logger.separator()
console.log('Simple logger (no timestamp/level):')
simpleLogger.info('Just a simple message')
simpleLogger.error('Simple error message')
